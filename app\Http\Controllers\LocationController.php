<?php

namespace App\Http\Controllers;

use App\Events\LocationCaptured;
use App\Models\Task;
use App\Models\TaskLocation;
use App\Models\GeofenceZone;
use App\Models\LocationAnalytics;
use App\Models\User;
use App\Services\AdvancedLocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class LocationController extends Controller
{
    protected $user;
    protected $workspace;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $this->user = getAuthenticatedUser();
            $this->workspace = \App\Models\Workspace::find(getWorkspaceId());
            return $next($request);
        });
    }

    /**
     * Store location data for a task action
     */
    public function store(Request $request)
    {
        try {
            // Check if user has location tracking enabled
            if (!isLocationTrackingEnabled($this->user->id)) {
                return response()->json([
                    'error' => false,
                    'message' => 'Location tracking is disabled for this user.',
                    'location_disabled' => true
                ]);
            }

            // Validate the request
            $validated = $request->validate([
                'task_id' => 'required|exists:tasks,id',
                'latitude' => 'required|numeric|between:-90,90',
                'longitude' => 'required|numeric|between:-180,180',
                'action' => ['required', Rule::in(TaskLocation::ACTIONS)],
            ]);

            // Verify user has access to the task
            $task = Task::findOrFail($validated['task_id']);
            
            // Check if user is assigned to the task or is the creator
            $hasAccess = $task->users()->where('user_id', $this->user->id)->exists() 
                        || $task->created_by == $this->user->id
                        || $this->user->hasRole('admin');

            if (!$hasAccess) {
                return response()->json([
                    'error' => true,
                    'message' => 'You do not have access to this task.'
                ], 403);
            }

            // Create the location record
            $locationData = [
                'user_id' => $this->user->id,
                'task_id' => $validated['task_id'],
                'latitude' => $validated['latitude'],
                'longitude' => $validated['longitude'],
                'action' => $validated['action'],
            ];

            $taskLocation = TaskLocation::create($locationData);

            // Load relationships for broadcasting
            $taskLocation->load(['task:id,title', 'user:id,first_name,last_name']);

            // Broadcast the location capture event
            broadcast(new LocationCaptured($taskLocation, $this->workspace->id));

            return response()->json([
                'error' => false,
                'message' => 'Location captured successfully.',
                'data' => [
                    'id' => $taskLocation->id,
                    'coordinates' => $taskLocation->coordinates,
                    'action' => $taskLocation->action_label,
                    'timestamp' => $taskLocation->created_at->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'error' => true,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to capture location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get location history for a specific task
     */
    public function getTaskLocations(Request $request, $taskId)
    {
        try {
            $task = Task::findOrFail($taskId);
            
            // Check if user has access to view this task's locations
            $hasAccess = $task->users()->where('user_id', $this->user->id)->exists() 
                        || $task->created_by == $this->user->id
                        || $this->user->hasRole(['admin', 'manager']);

            if (!$hasAccess) {
                return response()->json([
                    'error' => true,
                    'message' => 'You do not have access to view this task\'s locations.'
                ], 403);
            }

            $locations = TaskLocation::with(['user:id,first_name,last_name'])
                ->where('task_id', $taskId)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($location) {
                    return [
                        'id' => $location->id,
                        'user' => $location->user->first_name . ' ' . $location->user->last_name,
                        'coordinates' => $location->coordinates,
                        'latitude' => $location->latitude,
                        'longitude' => $location->longitude,
                        'action' => $location->action_label,
                        'timestamp' => $location->created_at->format('Y-m-d H:i:s'),
                        'date' => $location->created_at->format('M d, Y'),
                        'time' => $location->created_at->format('h:i A')
                    ];
                });

            return response()->json([
                'error' => false,
                'data' => $locations
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to retrieve locations: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's own location history
     */
    public function getUserLocations(Request $request)
    {
        try {
            $query = TaskLocation::with(['task:id,title', 'user:id,first_name,last_name'])
                ->where('user_id', $this->user->id);

            // Apply date filter if provided
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('created_at', [
                    $request->start_date . ' 00:00:00',
                    $request->end_date . ' 23:59:59'
                ]);
            }

            // Apply action filter if provided
            if ($request->has('action') && $request->action !== '') {
                $query->where('action', $request->action);
            }

            $locations = $query->orderBy('created_at', 'desc')
                ->paginate(20)
                ->through(function ($location) {
                    return [
                        'id' => $location->id,
                        'task' => $location->task->title,
                        'coordinates' => $location->coordinates,
                        'latitude' => $location->latitude,
                        'longitude' => $location->longitude,
                        'action' => $location->action_label,
                        'timestamp' => $location->created_at->format('Y-m-d H:i:s'),
                        'date' => $location->created_at->format('M d, Y'),
                        'time' => $location->created_at->format('h:i A')
                    ];
                });

            return response()->json([
                'error' => false,
                'data' => $locations
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to retrieve your locations: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Admin dashboard for location logs
     */
    public function adminDashboard(Request $request)
    {
        // Check if user has admin access
        if (!$this->user->hasRole(['admin', 'manager'])) {
            abort(403, 'Unauthorized access to location dashboard.');
        }

        // Get users based on role-based access control
        $accessibleUserIds = getAccessibleUserIds($this->user);
        $users = User::whereIn('id', $accessibleUserIds)
            ->select('id', 'first_name', 'last_name')
            ->get();

        $actions = TaskLocation::ACTIONS;

        return view('location.admin-dashboard', compact('users', 'actions'));
    }

    /**
     * Get location logs for admin dashboard
     */
    public function getLocationLogs(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access to location logs.'
                ], 403);
            }

            // Apply role-based access control
            $accessibleUserIds = getAccessibleUserIds($this->user);



            // Get users in the current workspace
            $workspaceUserIds = \DB::table('user_workspace')
                ->where('workspace_id', $this->workspace->id)
                ->pluck('user_id')
                ->toArray();

            // Intersect with accessible user IDs
            $filteredUserIds = array_intersect($accessibleUserIds, $workspaceUserIds);

            $query = TaskLocation::with(['task:id,title', 'user:id,first_name,last_name'])
                ->whereIn('user_id', $filteredUserIds);

            // Apply user filter if provided
            if ($request->filled('user_id') && is_numeric($request->user_id)) {
                $query->where('user_id', $request->user_id);
            }

            // Apply date filter if provided
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('created_at', [
                    $request->start_date . ' 00:00:00',
                    $request->end_date . ' 23:59:59'
                ]);
            }

            // Apply action filter if provided
            if ($request->filled('action')) {
                $query->where('action', $request->action);
            }

            // Apply task filter if provided
            if ($request->has('task_search') && $request->task_search !== '') {
                $query->whereHas('task', function($q) use ($request) {
                    $q->where('title', 'like', '%' . $request->task_search . '%');
                });
            }

            // Get total count for pagination
            $total = $query->count();

            // Apply sorting
            $sortBy = $request->get('sort', 'created_at');
            $sortOrder = $request->get('order', 'desc');

            // Validate sort order
            if (!in_array(strtolower($sortOrder), ['asc', 'desc'])) {
                $sortOrder = 'desc';
            }

            $query->orderBy($sortBy, $sortOrder);

            // Apply pagination
            $limit = $request->get('limit', 20);
            $offset = $request->get('offset', 0);
            $locations = $query->skip($offset)->take($limit)->get();

            $formattedLocations = $locations->map(function ($location) {
                return [
                    'id' => $location->id,
                    'user' => $location->user->first_name . ' ' . $location->user->last_name,
                    'user_id' => $location->user_id,
                    'task' => $location->task->title,
                    'task_id' => $location->task_id,
                    'coordinates' => $location->coordinates,
                    'latitude' => $location->latitude,
                    'longitude' => $location->longitude,
                    'action' => $location->action_label,
                    'action_raw' => $location->action,
                    'timestamp' => $location->created_at->format('Y-m-d H:i:s'),
                    'date' => $location->created_at->format('M d, Y'),
                    'time' => $location->created_at->format('h:i A'),
                    'created_at' => $location->created_at->toISOString()
                ];
            });

            // Handle CSV export
            if ($request->get('export') === 'csv') {
                return $this->exportLocationLogsCsv($query);
            }

            return response()->json([
                'error' => false,
                'data' => [
                    'rows' => $formattedLocations,
                    'total' => $total
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to retrieve location logs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export location logs to CSV
     */
    private function exportLocationLogsCsv($query)
    {
        $locations = $query->get();

        // Check if there are any location logs to export
        if ($locations->isEmpty()) {
            // Return an HTML page with JavaScript to show the message and close the tab
            $html = '<!DOCTYPE html>
<html>
<head>
    <title>Export Result</title>
    <script src="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css">
</head>
<body>
    <script>
        if (window.opener) {
            // If opened in a new tab, notify parent window
            if (window.opener.toastr) {
                window.opener.toastr.error("No location logs found to export.");
            } else if (window.opener.alert) {
                window.opener.alert("No location logs found to export.");
            }
            window.close();
        } else {
            // Fallback if not opened in new tab
            alert("No location logs found to export.");
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }
    </script>
</body>
</html>';
            return response($html, 200, ['Content-Type' => 'text/html']);
        }

        $filename = 'location_logs_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($locations) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'User',
                'Task',
                'Action',
                'Latitude',
                'Longitude',
                'Coordinates',
                'Date',
                'Time',
                'Timestamp'
            ]);

            // CSV data
            foreach ($locations as $location) {
                fputcsv($file, [
                    $location->user->first_name . ' ' . $location->user->last_name,
                    $location->task->title,
                    $location->action_label,
                    $location->latitude,
                    $location->longitude,
                    $location->coordinates,
                    $location->created_at->format('M d, Y'),
                    $location->created_at->format('h:i A'),
                    $location->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Map dashboard for location visualization
     */
    public function mapDashboard(Request $request)
    {
        // Check if user has admin access
        if (!$this->user->hasRole(['admin', 'manager'])) {
            abort(403, 'Unauthorized access to location map dashboard.');
        }

        // Get users based on role-based access control
        $accessibleUserIds = getAccessibleUserIds($this->user);
        $users = User::whereIn('id', $accessibleUserIds)
            ->select('id', 'first_name', 'last_name')
            ->get();

        $actions = TaskLocation::ACTIONS;

        return view('location.map-dashboard', compact('users', 'actions'));
    }

    /**
     * Get location data for map visualization
     */
    public function getMapData(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access to location map data.'
                ], 403);
            }

            // Apply role-based access control
            $accessibleUserIds = getAccessibleUserIds($this->user);

            // Get users in the current workspace
            $workspaceUserIds = \DB::table('user_workspace')
                ->where('workspace_id', $this->workspace->id)
                ->pluck('user_id')
                ->toArray();

            // Intersect with accessible user IDs
            $filteredUserIds = array_intersect($accessibleUserIds, $workspaceUserIds);

            $query = TaskLocation::with(['task:id,title', 'user:id,first_name,last_name'])
                ->whereIn('user_id', $filteredUserIds);

            // Apply user filter if provided
            if ($request->filled('user_id') && is_numeric($request->user_id)) {
                $query->where('user_id', $request->user_id);
            }

            // Apply date filter if provided
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('created_at', [
                    $request->start_date . ' 00:00:00',
                    $request->end_date . ' 23:59:59'
                ]);
            }

            // Apply action filter if provided
            if ($request->filled('action')) {
                $query->where('action', $request->action);
            }

            // Apply task filter if provided
            if ($request->has('task_search') && $request->task_search !== '') {
                $query->whereHas('task', function($q) use ($request) {
                    $q->where('title', 'like', '%' . $request->task_search . '%');
                });
            }

            // Handle CSV export
            if ($request->get('export') === 'csv') {
                return $this->exportLocationLogsCsv($query);
            }

            // Limit results for performance (max 1000 points)
            $locations = $query->orderBy('created_at', 'desc')
                ->limit(1000)
                ->get();

            $mapData = $locations->map(function ($location) {
                return [
                    'id' => $location->id,
                    'lat' => (float) $location->latitude,
                    'lng' => (float) $location->longitude,
                    'user' => $location->user->first_name . ' ' . $location->user->last_name,
                    'user_id' => $location->user_id,
                    'task' => $location->task->title,
                    'task_id' => $location->task_id,
                    'action' => $location->action_label,
                    'action_raw' => $location->action,
                    'timestamp' => $location->created_at->format('Y-m-d H:i:s'),
                    'date' => $location->created_at->format('M d, Y'),
                    'time' => $location->created_at->format('h:i A'),
                    'coordinates' => $location->coordinates
                ];
            });

            return response()->json([
                'error' => false,
                'data' => $mapData,
                'count' => $mapData->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to retrieve map data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Live tracking dashboard for currently active employees
     */
    public function liveTrackingDashboard(Request $request)
    {
        // Check if user has admin access
        if (!$this->user->hasRole(['admin', 'manager'])) {
            abort(403, 'Unauthorized access to live tracking dashboard.');
        }

        return view('location.live-tracking');
    }

    /**
     * Get live tracking data for currently checked-in employees
     */
    public function getLiveTrackingData(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            // Apply role-based access control
            $accessibleUserIds = getAccessibleUserIds($this->user);

            // Get users in the current workspace
            $workspaceUserIds = \DB::table('user_workspace')
                ->where('workspace_id', $this->workspace->id)
                ->pluck('user_id')
                ->toArray();

            // Intersect with accessible user IDs
            $filteredUserIds = array_intersect($accessibleUserIds, $workspaceUserIds);

            // Get currently checked-in employees (latest action is 'checkin')
            $activeEmployees = TaskLocation::with(['task:id,title', 'user:id,first_name,last_name'])
                ->whereIn('user_id', $filteredUserIds)
                ->whereIn('id', function($query) {
                    $query->select(DB::raw('MAX(id)'))
                        ->from('task_locations')
                        ->groupBy('user_id');
                })
                ->where('action', 'checkin')
                ->where('created_at', '>=', now()->subHours(24)) // Only show check-ins from last 24 hours
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $activeEmployees->map(function ($location) {
                return [
                    'id' => $location->id,
                    'user_id' => $location->user_id,
                    'user' => $location->user->first_name . ' ' . $location->user->last_name,
                    'task_id' => $location->task_id,
                    'task' => $location->task->title,
                    'lat' => (float) $location->latitude,
                    'lng' => (float) $location->longitude,
                    'coordinates' => $location->coordinates,
                    'action' => $location->action_label,
                    'action_raw' => $location->action,
                    'time' => $location->created_at->format('h:i A'),
                    'date' => $location->created_at->format('M d, Y'),
                    'timestamp' => $location->created_at->toISOString(),
                    'duration' => $location->created_at->diffForHumans(),
                ];
            });

            return response()->json([
                'error' => false,
                'data' => $data,
                'total' => $data->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to load live tracking data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Task location timeline view
     */
    public function taskLocationTimeline(Request $request, $taskId)
    {
        try {
            // Check if user has access to this task
            $task = Task::findOrFail($taskId);

            // Check if user can view this task's location data
            if (!$this->user->hasRole(['admin', 'manager']) && $task->user_id !== $this->user->id) {
                abort(403, 'Unauthorized access to task location timeline.');
            }

            // For managers, check if they can access this user's data
            if ($this->user->hasRole('manager') && !$this->user->hasRole('admin')) {
                $accessibleUserIds = getAccessibleUserIds($this->user);
                if (!in_array($task->user_id, $accessibleUserIds)) {
                    abort(403, 'Unauthorized access to task location timeline.');
                }
            }

            return view('location.task-timeline', compact('task'));

        } catch (\Exception $e) {
            abort(404, 'Task not found or access denied.');
        }
    }

    /**
     * Get task location timeline data
     */
    public function getTaskLocationTimelineData(Request $request, $taskId)
    {
        try {
            // Check if user has access to this task
            $task = Task::findOrFail($taskId);

            // Check if user can view this task's location data
            if (!$this->user->hasRole(['admin', 'manager']) && $task->user_id !== $this->user->id) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            // For managers, check if they can access this user's data
            if ($this->user->hasRole('manager') && !$this->user->hasRole('admin')) {
                $accessibleUserIds = getAccessibleUserIds($this->user);
                if (!in_array($task->user_id, $accessibleUserIds)) {
                    return response()->json([
                        'error' => true,
                        'message' => 'Unauthorized access.'
                    ], 403);
                }
            }

            // Get all location data for this task
            $locations = TaskLocation::with(['user:id,first_name,last_name'])
                ->where('task_id', $taskId)
                ->orderBy('created_at', 'asc')
                ->get();

            $timelineData = $locations->map(function ($location) {
                return [
                    'id' => $location->id,
                    'user_id' => $location->user_id,
                    'user' => $location->user->first_name . ' ' . $location->user->last_name,
                    'lat' => (float) $location->latitude,
                    'lng' => (float) $location->longitude,
                    'coordinates' => $location->coordinates,
                    'action' => $location->action_label,
                    'action_raw' => $location->action,
                    'time' => $location->created_at->format('h:i A'),
                    'date' => $location->created_at->format('M d, Y'),
                    'datetime' => $location->created_at->format('Y-m-d H:i:s'),
                    'timestamp' => $location->created_at->toISOString(),
                    'relative_time' => $location->created_at->diffForHumans(),
                ];
            });

            // Calculate timeline statistics
            $stats = [
                'total_locations' => $locations->count(),
                'task_created_count' => $locations->where('action', 'task_created')->count(),
                'checkin_count' => $locations->where('action', 'checkin')->count(),
                'checkout_count' => $locations->where('action', 'checkout')->count(),
                'first_location' => $locations->first() ? $locations->first()->created_at->format('M d, Y h:i A') : null,
                'last_location' => $locations->last() ? $locations->last()->created_at->format('M d, Y h:i A') : null,
                'duration' => $locations->count() > 1 ?
                    $locations->first()->created_at->diffForHumans($locations->last()->created_at, true) : null,
            ];

            return response()->json([
                'error' => false,
                'data' => $timelineData,
                'stats' => $stats,
                'task' => [
                    'id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->description,
                    'status' => $task->status,
                    'user' => $task->user->first_name . ' ' . $task->user->last_name,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to load task location timeline: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Advanced location capture with geofencing and analytics
     */
    public function storeAdvanced(Request $request)
    {
        try {
            $request->validate([
                'task_id' => 'required|exists:tasks,id',
                'latitude' => 'required|numeric|between:-90,90',
                'longitude' => 'required|numeric|between:-180,180',
                'accuracy' => 'nullable|numeric|min:0',
                'altitude' => 'nullable|numeric',
                'speed' => 'nullable|numeric|min:0',
                'heading' => 'nullable|numeric|between:0,360',
                'action' => ['required', Rule::in(TaskLocation::ACTIONS)],
                'tracking_type' => ['nullable', Rule::in(TaskLocation::TRACKING_TYPES)],
                'location_source' => ['nullable', Rule::in(TaskLocation::LOCATION_SOURCES)],
                'battery_level' => 'nullable|integer|between:0,100',
                'device_type' => 'nullable|string',
                'network_type' => 'nullable|string',
            ]);

            $locationService = new AdvancedLocationService();

            $location = $locationService->processAdvancedLocation(
                $request->all(),
                $this->user->id,
                $request->task_id,
                $this->workspace->id
            );

            return response()->json([
                'error' => false,
                'message' => 'Advanced location captured successfully',
                'location' => [
                    'id' => $location->id,
                    'coordinates' => $location->coordinates,
                    'address' => $location->formatted_address,
                    'action' => $location->action_label,
                    'accuracy_status' => $location->accuracy_status,
                    'geofence_events' => $location->geofence_zones,
                    'is_significant' => $location->is_significant_location,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to capture advanced location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Heat map dashboard
     */
    public function heatMapDashboard(Request $request)
    {
        // Check if user has admin access
        if (!$this->user->hasRole(['admin', 'manager'])) {
            abort(403, 'Unauthorized access to heat map dashboard.');
        }

        return view('location.heat-map');
    }

    /**
     * Get heat map data
     */
    public function getHeatMapData(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $startDate = $request->get('start_date', now()->subDays(7)->toDateString());
            $endDate = $request->get('end_date', now()->toDateString());
            $userId = $request->get('user_id');

            // Apply role-based access control
            $accessibleUserIds = getAccessibleUserIds($this->user);

            // Get users in the current workspace
            $workspaceUserIds = \DB::table('user_workspace')
                ->where('workspace_id', $this->workspace->id)
                ->pluck('user_id')
                ->toArray();

            // Intersect with accessible user IDs
            $filteredUserIds = array_intersect($accessibleUserIds, $workspaceUserIds);

            $query = TaskLocation::whereIn('user_id', $filteredUserIds)
                ->whereBetween('created_at', [$startDate, $endDate]);

            if ($userId && in_array($userId, $accessibleUserIds)) {
                $query->where('user_id', $userId);
            }

            $locations = $query->get();

            $locationService = new AdvancedLocationService();
            $heatMapData = $locationService->generateHeatMapData(
                $userId ?: $accessibleUserIds,
                $startDate,
                $endDate
            );

            return response()->json([
                'error' => false,
                'heat_map_data' => $heatMapData,
                'total_locations' => $locations->count(),
                'date_range' => [
                    'start' => $startDate,
                    'end' => $endDate
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to load heat map data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Analytics dashboard
     */
    public function analyticsDashboard(Request $request)
    {
        // Check if user has admin access
        if (!$this->user->hasRole(['admin', 'manager'])) {
            abort(403, 'Unauthorized access to analytics dashboard.');
        }

        return view('location.analytics');
    }

    /**
     * Get analytics data
     */
    public function getAnalyticsData(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $startDate = $request->get('start_date', now()->subDays(30)->toDateString());
            $endDate = $request->get('end_date', now()->toDateString());
            $userId = $request->get('user_id');

            // Apply role-based access control
            $accessibleUserIds = getAccessibleUserIds($this->user);

            $locationService = new AdvancedLocationService();

            if ($userId && in_array($userId, $accessibleUserIds)) {
                $analytics = $locationService->getAnalyticsSummary(
                    $userId,
                    $this->workspace->id,
                    $startDate,
                    $endDate
                );
            } else {
                // Aggregate analytics for all accessible users
                $analytics = [
                    'total_distance' => 0,
                    'total_duration' => 0,
                    'total_locations' => 0,
                    'avg_efficiency_score' => 0,
                    'total_geofence_events' => 0,
                    'unique_days' => 0,
                ];

                foreach ($accessibleUserIds as $uid) {
                    $userAnalytics = $locationService->getAnalyticsSummary(
                        $uid,
                        $this->workspace->id,
                        $startDate,
                        $endDate
                    );

                    $analytics['total_distance'] += $userAnalytics['total_distance'];
                    $analytics['total_duration'] += $userAnalytics['total_duration'];
                    $analytics['total_locations'] += $userAnalytics['total_locations'];
                    $analytics['total_geofence_events'] += $userAnalytics['total_geofence_events'];
                    $analytics['unique_days'] = max($analytics['unique_days'], $userAnalytics['unique_days']);
                }

                // Calculate average efficiency
                $analytics['avg_efficiency_score'] = count($accessibleUserIds) > 0 ?
                    $analytics['avg_efficiency_score'] / count($accessibleUserIds) : 0;
            }

            return response()->json([
                'error' => false,
                'analytics' => $analytics,
                'date_range' => [
                    'start' => $startDate,
                    'end' => $endDate
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to load analytics data: ' . $e->getMessage()
            ], 500);
        }
    }
}
